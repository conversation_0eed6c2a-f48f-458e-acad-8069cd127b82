# ProManage Database Architecture

## Overview

ProManage uses a centralized database connectivity architecture that provides a clean separation between the UI layer and data access layer. The system is designed around PostgreSQL as the primary database with a centralized connection management system.

## Architecture Components

### 1. Centralized Connection Management (`Modules/Connections/`)

The database connectivity layer is centralized in the `Modules/Connections/` directory:

- **DatabaseConnectionManager.cs**: Singleton class that manages persistent database connections
- **DatabaseTransactionService.cs**: Handles database operations with transaction management
- **QueryExecutor.cs**: Executes SQL queries loaded from procedure files

### 2. Data Access Layer (`Modules/Data/`)

The data access layer contains repository classes and business logic:

- **EstimateRepository.cs**: Repository for estimate-related database operations
- **EstimateQueryService.cs**: Service for executing estimate-specific SQL queries
- **UserManager.cs**: Manages user authentication and session data
- **SQLQueries.cs**: Contains SQL query constants and organization

### 3. Data Models (`Modules/Models/`)

Data models represent the business entities:

- **EstimateHeader.cs**: Represents estimate header information
- **EstimateDetail.cs**: Represents estimate line items
- **User.cs**: Represents user information

### 4. SQL Procedures (`Modules/Procedures/`)

SQL queries are stored as separate files organized by module:

```
Modules/Procedures/
├── Estimate/
│   ├── EstimateCRUD.sql
│   ├── EstimateNavigation.sql
│   ├── EstimateRetrieval.sql
│   └── EstimateUtilities.sql
├── User/
│   ├── GetUser.sql
│   ├── SaveUser.sql
│   └── ValidateUser.sql
└── SQLQuery/
    ├── GetAllTables.sql
    ├── GetTableColumns.sql
    └── GetTableData.sql
```

## Data Flow Architecture

### 1. Form → Repository → Database Flow

```
Windows Forms (UI Layer)
    ↓
Repository Classes (Data Access Layer)
    ↓
DatabaseConnectionManager (Connection Layer)
    ↓
PostgreSQL Database
```

### 2. Detailed Data Flow

1. **User Interaction**: User interacts with Windows Forms (EstimateForm, LoginForm, etc.)
2. **Repository Call**: Form calls appropriate repository method (e.g., `EstimateRepository.GetEstimateById()`)
3. **SQL Loading**: Repository loads SQL from procedure files using `SQLQueryLoader`
4. **Connection Management**: Repository gets connection from `DatabaseConnectionManager.Instance`
5. **Query Execution**: SQL is executed using `QueryExecutor` or direct connection
6. **Data Mapping**: Results are mapped to model objects (EstimateHeader, EstimateDetail, etc.)
7. **UI Update**: Mapped data is returned to the form for display

## Connection Management

### DatabaseConnectionManager Features

- **Singleton Pattern**: Ensures single connection instance throughout application
- **Connection Pooling**: Manages connection pool with configurable settings
- **Automatic Reconnection**: Handles connection failures with exponential backoff
- **Connection Monitoring**: Tracks connection status and health
- **Configuration Loading**: Loads connection settings from app.config

### Connection Lifecycle

1. **Initialization**: Connection manager loads configuration on first access
2. **Connection Creation**: Creates new connections as needed using `CreateNewConnection()`
3. **Connection Testing**: Validates connections with `TestConnection()` and `TestCurrentConnection()`
4. **Connection Updates**: Updates connection settings with `UpdateConnection()`
5. **Connection Cleanup**: Automatically manages connection disposal and cleanup

## SQL Query Management

### Query Organization

SQL queries are organized by functional modules and stored as separate `.sql` files:

- **Module-based**: Each module (Estimate, User, SQLQuery) has its own folder
- **Operation-based**: Files are named by operation (Get, Save, Delete, etc.)
- **Named Queries**: Complex files can contain multiple named query sections

### Query Loading Process

1. **SQLQueryLoader**: Loads SQL content from procedure files
2. **Caching**: Queries are cached to avoid repeated file reads
3. **Named Query Extraction**: Supports extracting specific named queries from files
4. **Parameter Substitution**: Uses PostgreSQL parameter format (@paramName)

### Example Usage

```csharp
// Load a complete SQL file
string query = SQLQueryLoader.LoadQuery("Estimate", "GetAllEstimates");

// Load a named query from a file
string namedQuery = SQLQueryLoader.ExtractNamedQuery("Estimate", "SaveEstimate", "InsertHeader");

// Execute query with parameters
var parameters = new Dictionary<string, object> { { "@estimate_id", 1 } };
DataTable result = QueryExecutor.ExecuteQueryFromFile("Estimate", "GetEstimateById", parameters, out string errorMessage);
```

## Transaction Management

### DatabaseTransactionService

Provides transaction support for complex operations:

- **Transaction Context**: Manages begin/commit/rollback operations
- **Error Handling**: Automatic rollback on exceptions
- **Connection Management**: Creates and manages connections for transactions
- **Logging**: Comprehensive logging for debugging and monitoring

### Transaction Usage Pattern

```csharp
using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
{
    conn.Open();
    using (var transaction = conn.BeginTransaction())
    {
        try
        {
            // Perform multiple database operations
            // ...

            transaction.Commit();
        }
        catch (Exception)
        {
            transaction.Rollback();
            throw;
        }
    }
}
```

## Best Practices

### 1. Repository Pattern

- All database access goes through repository classes
- Repositories handle SQL loading, execution, and data mapping
- Forms never directly access database connections

### 2. Connection Management

- Always use `DatabaseConnectionManager.Instance.CreateNewConnection()`
- Wrap connections in `using` statements for proper disposal
- Use transactions for multi-step operations

### 3. SQL Organization

- Store SQL in separate files by module and operation
- Use parameterized queries to prevent SQL injection
- Name files descriptively (GetEstimateById.sql, SaveEstimate.sql)

### 4. Error Handling

- Always handle database exceptions gracefully
- Log errors for debugging and monitoring
- Provide meaningful error messages to users

### 5. Performance Considerations

- Use connection pooling for better performance
- Cache frequently used SQL queries
- Implement proper indexing in database schema
- Use transactions judiciously to avoid long locks

## Security Considerations

- **Parameterized Queries**: All queries use parameters to prevent SQL injection
- **Connection String Security**: Connection strings stored securely in app.config
- **User Authentication**: Centralized user management through UserManager
- **Access Control**: Repository pattern provides controlled data access

## Monitoring and Debugging

- **Comprehensive Logging**: All database operations are logged using Debug.WriteLine
- **Connection Status Monitoring**: Real-time connection status tracking
- **Error Reporting**: Detailed error messages with stack traces
- **Performance Metrics**: Connection timing and query performance tracking

## Migration Notes

### Recent Changes (Database Connectivity Reorganization)

As part of the cleanup process, the database connectivity components have been reorganized:

**Files Moved to `Modules/Connections/`:**
- `DatabaseConnectionManager.cs` (moved from `Modules/Data/`)
- `DatabaseTransactionService.cs` (moved from `Modules/Data/`)
- `QueryExecutor.cs` (moved from `Modules/Data/`)

**Namespace Changes:**
- Old namespace: `ProManage.Modules.Data`
- New namespace: `ProManage.Modules.Connections`

**Files Updated with New Namespace References:**
- `Program.cs`
- `Forms/LoginForm.cs`
- `Forms/SQLQueryForm.cs`
- `Forms/DatabaseForm.cs`
- `Forms/EstimateForm.cs`
- `Modules/Data/EstimateRepository.cs`
- `Modules/Data/EstimateQueryService.cs`

**Project File Updates:**
- Updated `ProManage.csproj` to reference files in new locations
- Removed references to deleted unused helper files

### Files Removed During Cleanup

**Unused Helper Files (14 files removed):**
- `AccordionHelper.cs`
- `DevExpressHelper.cs`
- `GridHelper.cs`
- `PropertyGridHelper.cs`
- `EditorsHelper.cs`
- `FormControlHelper.cs`
- `FormDesignHelper.cs`
- `ReportDesignerHelper.cs`
- `RibbonHelper.cs`
- `SerialNumberHelper.cs`
- `SyncfusionHelper.cs`
- `TabbedMdiHelper.cs`
- `ThemeManager.cs`
- `ValidationHelper.cs`

**Unused Data/UI Files (8 files removed):**
- `ConnectionStatusType.cs`
- `DatabaseUtilities.cs`
- `NavigationManager.cs`
- `NavigationManagerImplementation.cs`
- `StatusMessageType.cs`
- `ReportManager.cs`
- `ConversionHelper.cs`
- `FormState.cs`

**Other Files (3 files removed):**
- `ModuleNamespaces.cs`
- `DatabaseHelper.cs` (duplicate functionality)
- `QueryExecutor.cs` (duplicate in Helpers folder)

**Empty Directories (3 directories removed):**
- `Modules/EventHandlers/`
- `Modules/Validation/`

## Post-Migration Verification

After completing the reorganization, verify the following:

1. **Build Verification**: Ensure the project compiles without errors
2. **Namespace Resolution**: All using statements should resolve correctly
3. **Database Connectivity**: Test database connections work properly
4. **Form Functionality**: Verify all forms load and function correctly
5. **Repository Operations**: Test CRUD operations through repositories

## Troubleshooting Common Issues

### Namespace Not Found Errors
If you encounter "The type or namespace name 'Connections' does not exist" errors:
1. Clean and rebuild the solution
2. Verify all using statements reference `ProManage.Modules.Connections`
3. Check that the project file includes the moved files correctly

### Missing Reference Errors
If you encounter missing reference errors:
1. Ensure all moved files have the correct namespace declaration
2. Verify project file references point to the new file locations
3. Check for any remaining references to old namespaces

### Database Connection Issues
If database connections fail after reorganization:
1. Verify DatabaseConnectionManager is accessible
2. Check that connection strings are properly configured
3. Test database connectivity using the DatabaseForm
