# Task 11: User Master Form Permission Integration

## Objective
Integrate the existing UserMasterForm with the new RBAC system. Add a permissions tab that displays effective permissions for the user and provides a link to manage permissions. Enhance the form to work with role assignments and global permissions.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-10

## Estimated Time
2 hours

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)
- Task 10: Role Master Form Enhancement

## Files to Modify
- `Forms/MainForms/UserMasterForm.cs` (enhance existing)
- `Forms/MainForms/UserMasterForm.Designer.cs` (enhance existing)

## Enhancement Overview

### Current UserMasterForm Structure
Based on ProManage patterns and memories, the existing form likely has:
- User details (username, full name, email, etc.)
- Department, phone, designation, short_name fields
- Basic user management functionality
- Tab-based design

### Enhanced Structure
Add/modify tabs:
- **Existing tabs** (User Details, etc.)
- **Tab: Role & Permissions** (new/enhanced)
- **Tab: Global Permissions** (new for user management permissions)

## Implementation Plan

### 1. Add Role Selection to User Details

Enhance user details section:
```csharp
private DevExpress.XtraEditors.LookUpEdit lookUpRole;
private DevExpress.XtraEditors.LabelControl lblRole;
```

### 2. Add Permissions Tab

Add new tab for permission display:
```csharp
private DevExpress.XtraTab.XtraTabPage tabPagePermissions;
private DevExpress.XtraGrid.GridControl gridControlUserPermissions;
private DevExpress.XtraGrid.Views.Grid.GridView gridViewUserPermissions;
private DevExpress.XtraEditors.SimpleButton btnManagePermissions;
private DevExpress.XtraEditors.SimpleButton btnResetToRole;
private DevExpress.XtraEditors.LabelControl lblEffectivePermissions;
```

### 3. Add Global Permissions Tab

Add tab for global user management permissions:
```csharp
private DevExpress.XtraTab.XtraTabPage tabPageGlobalPermissions;
private DevExpress.XtraEditors.CheckEdit chkCanCreateUsers;
private DevExpress.XtraEditors.CheckEdit chkCanEditUsers;
private DevExpress.XtraEditors.CheckEdit chkCanDeleteUsers;
private DevExpress.XtraEditors.CheckEdit chkCanPrintUsers;
private DevExpress.XtraEditors.GroupControl groupGlobalPermissions;
```

## Implementation

### Enhanced UserMasterForm.cs

```csharp
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Connections;

public partial class UserMasterForm : Form
{
    private readonly PermissionDatabaseService _permissionDbService;
    private List<EffectivePermission> _userPermissions;
    private GlobalPermission _globalPermissions;
    private bool _permissionsLoaded = false;
    
    // Add to constructor
    private void InitializePermissionEnhancements()
    {
        _permissionDbService = new PermissionDatabaseService();
        SetupRoleSelection();
        SetupPermissionsTab();
        SetupGlobalPermissionsTab();
    }
    
    #region Role Selection Setup
    
    private void SetupRoleSelection()
    {
        // Setup role lookup
        lookUpRole.Properties.DataSource = LoadRoles();
        lookUpRole.Properties.DisplayMember = "RoleName";
        lookUpRole.Properties.ValueMember = "RoleId";
        lookUpRole.Properties.Columns.Clear();
        lookUpRole.Properties.Columns.Add(new DevExpress.XtraEditors.Controls.LookUpColumnInfo("RoleName", "Role Name"));
        
        lookUpRole.EditValueChanged += LookUpRole_EditValueChanged;
    }
    
    private List<Role> LoadRoles()
    {
        try
        {
            return _permissionDbService.GetAllRoles();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading roles: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return new List<Role>();
        }
    }
    
    private void LookUpRole_EditValueChanged(object sender, EventArgs e)
    {
        if (_permissionsLoaded && lookUpRole.EditValue != null)
        {
            LoadUserPermissions();
        }
    }
    
    #endregion
    
    #region Permissions Tab Setup
    
    private void SetupPermissionsTab()
    {
        // Setup permissions grid
        SetupPermissionsGrid();
        
        // Setup buttons
        btnManagePermissions.Click += BtnManagePermissions_Click;
        btnResetToRole.Click += BtnResetToRole_Click;
        
        // Initially disable until user is loaded
        tabPagePermissions.PageEnabled = false;
    }
    
    private void SetupPermissionsGrid()
    {
        gridViewUserPermissions.OptionsView.ShowGroupPanel = false;
        gridViewUserPermissions.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
        gridViewUserPermissions.OptionsSelection.EnableAppearanceFocusedCell = false;
        gridViewUserPermissions.OptionsCustomization.AllowColumnMoving = false;
        gridViewUserPermissions.OptionsCustomization.AllowColumnResizing = true;
        
        // All columns read-only (this is display only)
        gridViewUserPermissions.OptionsBehavior.Editable = false;
        
        // Setup row styling for permission source
        gridViewUserPermissions.RowCellStyle += GridViewUserPermissions_RowCellStyle;
    }
    
    private void GridViewUserPermissions_RowCellStyle(object sender, DevExpress.XtraGrid.Views.Grid.RowCellStyleEventArgs e)
    {
        if (e.Column.FieldName == "Source") return;
        
        var source = gridViewUserPermissions.GetRowCellValue(e.RowHandle, "Source")?.ToString();
        
        if (source == "UserOverride")
        {
            e.Appearance.BackColor = Color.LightBlue;
            e.Appearance.ForeColor = Color.DarkBlue;
        }
        else if (source == "Role")
        {
            e.Appearance.BackColor = Color.LightGreen;
            e.Appearance.ForeColor = Color.DarkGreen;
        }
    }
    
    #endregion
    
    #region Global Permissions Tab Setup
    
    private void SetupGlobalPermissionsTab()
    {
        // Setup global permission checkboxes
        chkCanCreateUsers.CheckedChanged += GlobalPermission_CheckedChanged;
        chkCanEditUsers.CheckedChanged += GlobalPermission_CheckedChanged;
        chkCanDeleteUsers.CheckedChanged += GlobalPermission_CheckedChanged;
        chkCanPrintUsers.CheckedChanged += GlobalPermission_CheckedChanged;
        
        // Initially disable until user is loaded
        tabPageGlobalPermissions.PageEnabled = false;
    }
    
    private void GlobalPermission_CheckedChanged(object sender, EventArgs e)
    {
        if (_permissionsLoaded)
        {
            // Mark form as dirty
            EnableSaveButton();
        }
    }
    
    #endregion
    
    #region Permission Loading
    
    private void LoadUserPermissions()
    {
        var userId = GetCurrentUserId();
        if (userId <= 0) return;
        
        try
        {
            // Load effective permissions
            _userPermissions = PermissionService.GetUserEffectivePermissions(userId);
            
            // Convert to display format
            var displayPermissions = _userPermissions.Select(p => new
            {
                FormName = p.FormName,
                DisplayName = FormsConfigurationService.GetFormDisplayName(p.FormName),
                Read = p.ReadPermission ? "✓" : "✗",
                New = p.NewPermission ? "✓" : "✗",
                Edit = p.EditPermission ? "✓" : "✗",
                Delete = p.DeletePermission ? "✓" : "✗",
                Print = p.PrintPermission ? "✓" : "✗",
                Source = p.Source.ToString()
            }).ToList();
            
            gridControlUserPermissions.DataSource = displayPermissions;
            gridViewUserPermissions.BestFitColumns();
            
            // Load global permissions
            LoadGlobalPermissions(userId);
            
            tabPagePermissions.PageEnabled = true;
            tabPageGlobalPermissions.PageEnabled = true;
            _permissionsLoaded = true;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    private void LoadGlobalPermissions(int userId)
    {
        try
        {
            _globalPermissions = _permissionDbService.GetGlobalPermissions(userId);
            
            if (_globalPermissions != null)
            {
                chkCanCreateUsers.Checked = _globalPermissions.CanCreateUsers;
                chkCanEditUsers.Checked = _globalPermissions.CanEditUsers;
                chkCanDeleteUsers.Checked = _globalPermissions.CanDeleteUsers;
                chkCanPrintUsers.Checked = _globalPermissions.CanPrintUsers;
            }
            else
            {
                // No global permissions set - default to false
                chkCanCreateUsers.Checked = false;
                chkCanEditUsers.Checked = false;
                chkCanDeleteUsers.Checked = false;
                chkCanPrintUsers.Checked = false;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading global permissions: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    #endregion
    
    #region Button Events
    
    private void BtnManagePermissions_Click(object sender, EventArgs e)
    {
        var userId = GetCurrentUserId();
        if (userId <= 0)
        {
            MessageBox.Show("Please save the user first before managing permissions.", "Information", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }
        
        // Open Permission Management Form with this user selected
        var permissionForm = new PermissionManagementForm();
        
        // Set the user in the permission form (would need to add this functionality)
        // permissionForm.SetSelectedUser(userId);
        
        permissionForm.ShowDialog(this);
        
        // Refresh permissions after closing
        LoadUserPermissions();
    }
    
    private void BtnResetToRole_Click(object sender, EventArgs e)
    {
        var userId = GetCurrentUserId();
        if (userId <= 0) return;
        
        var result = MessageBox.Show(
            "This will remove all user permission overrides and revert to role permissions. Continue?",
            "Confirm Reset",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        
        if (result == DialogResult.Yes)
        {
            try
            {
                if (_permissionDbService.RemoveAllUserPermissionOverrides(userId))
                {
                    LoadUserPermissions();
                    MessageBox.Show("User permissions reset to role permissions successfully.", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // Clear permission cache
                    PermissionService.ClearUserPermissionCache(userId);
                }
                else
                {
                    MessageBox.Show("Failed to reset user permissions.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error resetting permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    #endregion
    
    #region Enhanced Save/Load Logic
    
    // Override or enhance existing save method
    protected override bool SaveUser()
    {
        // Call existing save logic first
        if (!base.SaveUser()) // Assuming base save method exists
            return false;
        
        // Save global permissions if changed
        if (_permissionsLoaded)
        {
            return SaveGlobalPermissions();
        }
        
        return true;
    }
    
    private bool SaveGlobalPermissions()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId <= 0) return true; // No user to save permissions for
            
            var globalPermissionUpdate = new GlobalPermissionUpdate
            {
                UserId = userId,
                CanCreateUsers = chkCanCreateUsers.Checked,
                CanEditUsers = chkCanEditUsers.Checked,
                CanDeleteUsers = chkCanDeleteUsers.Checked,
                CanPrintUsers = chkCanPrintUsers.Checked
            };
            
            var success = _permissionDbService.UpdateGlobalPermissions(globalPermissionUpdate);
            
            if (success)
            {
                // Clear permission cache
                PermissionService.ClearUserPermissionCache(userId);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving global permissions: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
    
    // Override or enhance existing load method
    protected override void LoadUser(int userId)
    {
        // Call existing load logic
        base.LoadUser(userId); // Assuming base load method exists
        
        // Load permissions
        LoadUserPermissions();
    }
    
    // Override or enhance existing new user method
    protected override void NewUser()
    {
        // Call existing new logic
        base.NewUser(); // Assuming base new method exists
        
        // Disable permissions tabs for new user
        tabPagePermissions.PageEnabled = false;
        tabPageGlobalPermissions.PageEnabled = false;
        _permissionsLoaded = false;
        
        // Set default role if available
        var roles = LoadRoles();
        var defaultRole = roles.FirstOrDefault(r => r.RoleName.Equals("User", StringComparison.OrdinalIgnoreCase));
        if (defaultRole != null)
        {
            lookUpRole.EditValue = defaultRole.RoleId;
        }
    }
    
    #endregion
    
    #region Helper Methods
    
    private int GetCurrentUserId()
    {
        // Implement based on existing form logic
        // This should return the currently selected/loaded user ID
        return 0; // TODO: Implement based on existing form structure
    }
    
    private void EnableSaveButton()
    {
        // Enable save button or mark form as dirty
        // Implement based on existing form patterns
    }
    
    #endregion
    
    #region Permission Checking
    
    // Override form load to check permissions
    protected override void OnLoad(EventArgs e)
    {
        base.OnLoad(e);
        
        // Check if current user has permission to access this form
        var currentUserId = GetCurrentLoggedInUserId(); // Implement based on session management
        
        if (!PermissionService.HasPermission(currentUserId, "UserMasterForm", PermissionType.Read))
        {
            MessageBox.Show("You do not have permission to access this form.", "Access Denied", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            this.Close();
            return;
        }
        
        // Set button permissions
        var canEdit = PermissionService.HasPermission(currentUserId, "UserMasterForm", PermissionType.Edit);
        var canCreate = PermissionService.HasPermission(currentUserId, "UserMasterForm", PermissionType.New);
        var canDelete = PermissionService.HasPermission(currentUserId, "UserMasterForm", PermissionType.Delete);
        
        // Apply permissions to form controls
        SetFormPermissions(canEdit, canCreate, canDelete);
        
        // Check global permissions for user management
        var canEditUsers = PermissionService.HasGlobalPermission(currentUserId, GlobalPermissionType.CanEditUsers);
        var canCreateUsers = PermissionService.HasGlobalPermission(currentUserId, GlobalPermissionType.CanCreateUsers);
        
        if (!canEditUsers && !canCreateUsers)
        {
            // Make form read-only
            SetFormReadOnly();
        }
    }
    
    private void SetFormPermissions(bool canEdit, bool canCreate, bool canDelete)
    {
        // Implement based on existing form structure
        // Enable/disable buttons and controls based on permissions
    }
    
    private void SetFormReadOnly()
    {
        // Make all controls read-only except navigation
        // Implement based on existing form structure
    }
    
    private int GetCurrentLoggedInUserId()
    {
        // Implement based on existing session/user management
        return 1; // TODO: Get from session or user manager
    }
    
    #endregion
}
```

### Designer Changes

Add to UserMasterForm.Designer.cs:

```csharp
private void InitializePermissionControls()
{
    // Role Selection (add to existing user details section)
    this.lblRole = new DevExpress.XtraEditors.LabelControl();
    this.lookUpRole = new DevExpress.XtraEditors.LookUpEdit();
    
    // Permissions Tab
    this.tabPagePermissions = new DevExpress.XtraTab.XtraTabPage();
    this.gridControlUserPermissions = new DevExpress.XtraGrid.GridControl();
    this.gridViewUserPermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
    this.btnManagePermissions = new DevExpress.XtraEditors.SimpleButton();
    this.btnResetToRole = new DevExpress.XtraEditors.SimpleButton();
    this.lblEffectivePermissions = new DevExpress.XtraEditors.LabelControl();
    
    // Global Permissions Tab
    this.tabPageGlobalPermissions = new DevExpress.XtraTab.XtraTabPage();
    this.groupGlobalPermissions = new DevExpress.XtraEditors.GroupControl();
    this.chkCanCreateUsers = new DevExpress.XtraEditors.CheckEdit();
    this.chkCanEditUsers = new DevExpress.XtraEditors.CheckEdit();
    this.chkCanDeleteUsers = new DevExpress.XtraEditors.CheckEdit();
    this.chkCanPrintUsers = new DevExpress.XtraEditors.CheckEdit();
    
    // Role Selection Layout
    this.lblRole.Location = new System.Drawing.Point(10, 100); // Adjust based on existing layout
    this.lblRole.Text = "Role:";
    
    this.lookUpRole.Location = new System.Drawing.Point(80, 97);
    this.lookUpRole.Size = new System.Drawing.Size(200, 20);
    
    // Permissions Tab Layout
    this.tabPagePermissions.Text = "Permissions";
    this.tabPagePermissions.Controls.Add(this.lblEffectivePermissions);
    this.tabPagePermissions.Controls.Add(this.gridControlUserPermissions);
    this.tabPagePermissions.Controls.Add(this.btnManagePermissions);
    this.tabPagePermissions.Controls.Add(this.btnResetToRole);
    
    this.lblEffectivePermissions.Location = new System.Drawing.Point(10, 10);
    this.lblEffectivePermissions.Text = "Effective Permissions (Green = Role, Blue = User Override):";
    
    this.gridControlUserPermissions.Location = new System.Drawing.Point(10, 35);
    this.gridControlUserPermissions.Size = new System.Drawing.Size(740, 300);
    this.gridControlUserPermissions.MainView = this.gridViewUserPermissions;
    
    this.btnManagePermissions.Location = new System.Drawing.Point(10, 345);
    this.btnManagePermissions.Size = new System.Drawing.Size(120, 23);
    this.btnManagePermissions.Text = "Manage Permissions";
    
    this.btnResetToRole.Location = new System.Drawing.Point(140, 345);
    this.btnResetToRole.Size = new System.Drawing.Size(100, 23);
    this.btnResetToRole.Text = "Reset to Role";
    
    // Global Permissions Tab Layout
    this.tabPageGlobalPermissions.Text = "Global Permissions";
    this.tabPageGlobalPermissions.Controls.Add(this.groupGlobalPermissions);
    
    this.groupGlobalPermissions.Location = new System.Drawing.Point(10, 10);
    this.groupGlobalPermissions.Size = new System.Drawing.Size(300, 150);
    this.groupGlobalPermissions.Text = "User Management Permissions";
    this.groupGlobalPermissions.Controls.Add(this.chkCanCreateUsers);
    this.groupGlobalPermissions.Controls.Add(this.chkCanEditUsers);
    this.groupGlobalPermissions.Controls.Add(this.chkCanDeleteUsers);
    this.groupGlobalPermissions.Controls.Add(this.chkCanPrintUsers);
    
    this.chkCanCreateUsers.Location = new System.Drawing.Point(10, 30);
    this.chkCanCreateUsers.Text = "Can Create Users";
    
    this.chkCanEditUsers.Location = new System.Drawing.Point(10, 55);
    this.chkCanEditUsers.Text = "Can Edit Users";
    
    this.chkCanDeleteUsers.Location = new System.Drawing.Point(10, 80);
    this.chkCanDeleteUsers.Text = "Can Delete Users";
    
    this.chkCanPrintUsers.Location = new System.Drawing.Point(10, 105);
    this.chkCanPrintUsers.Text = "Can Print User Reports";
}
```

## Acceptance Criteria

- [ ] Role selection dropdown integrated with user details
- [ ] Permissions tab showing effective permissions with color coding
- [ ] Global permissions tab for user management permissions
- [ ] "Manage Permissions" button opens PermissionManagementForm
- [ ] "Reset to Role" functionality removes user overrides
- [ ] Form permission checks on load (read/edit/create/delete)
- [ ] Global permission checks for user management operations
- [ ] Integration with existing save/load/new user logic
- [ ] Proper error handling and user feedback
- [ ] Permission cache clearing after changes

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)
- Task 10: Role Master Form Enhancement

## Next Tasks
This task enables:
- Task 13: MainFrame Ribbon Permission Filtering
- Task 14: Individual Form Permission Checks
- Task 15: Global Permission Implementation
