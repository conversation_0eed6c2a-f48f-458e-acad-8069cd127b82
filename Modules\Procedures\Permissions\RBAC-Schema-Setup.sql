-- RBAC-Schema-Setup.sql
-- Database schema setup for Role-Based Access Control (RBAC) system
-- Converts SQL Server syntax to PostgreSQL for ProManage application

-- [CreateRolesTable] --
-- Create roles table if it doesn't exist
CREATE TABLE IF NOT EXISTS roles (
    role_id SERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add comments for documentation
COMMENT ON TABLE roles IS 'System roles for RBAC permission system';
COMMENT ON COLUMN roles.role_id IS 'Primary key for roles';
COMMENT ON COLUMN roles.role_name IS 'Unique role name';
COMMENT ON COLUMN roles.description IS 'Role description';
COMMENT ON COLUMN roles.is_active IS 'Whether role is active';
COMMENT ON COLUMN roles.created_date IS 'Role creation timestamp';
COMMENT ON COLUMN roles.modified_date IS 'Role last modification timestamp';
-- [End] --

-- [CreateRolePermissionsTable] --
-- Create role_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS role_permissions (
    permission_id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL,
    form_name VARCHAR(100) NOT NULL,
    read_permission BOOLEAN DEFAULT FALSE,
    new_permission BOOLEAN DEFAULT FALSE,
    edit_permission BOOLEAN DEFAULT FALSE,
    delete_permission BOOLEAN DEFAULT FALSE,
    print_permission BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    UNIQUE(role_id, form_name)
);

-- Add comments for documentation
COMMENT ON TABLE role_permissions IS 'Form-level permissions for each role';
COMMENT ON COLUMN role_permissions.permission_id IS 'Primary key for role permissions';
COMMENT ON COLUMN role_permissions.role_id IS 'Foreign key to roles table';
COMMENT ON COLUMN role_permissions.form_name IS 'Name of the form';
COMMENT ON COLUMN role_permissions.read_permission IS 'Can view/read form data';
COMMENT ON COLUMN role_permissions.new_permission IS 'Can create new records';
COMMENT ON COLUMN role_permissions.edit_permission IS 'Can edit existing records';
COMMENT ON COLUMN role_permissions.delete_permission IS 'Can delete records';
COMMENT ON COLUMN role_permissions.print_permission IS 'Can print/export data';
-- [End] --

-- [CreateUserPermissionsTable] --
-- Create user_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_permissions (
    user_permission_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    form_name VARCHAR(100) NOT NULL,
    read_permission BOOLEAN NULL,     -- NULL = inherit from role
    new_permission BOOLEAN NULL,      -- NULL = inherit from role
    edit_permission BOOLEAN NULL,     -- NULL = inherit from role
    delete_permission BOOLEAN NULL,   -- NULL = inherit from role
    print_permission BOOLEAN NULL,    -- NULL = inherit from role
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id, form_name)
);

-- Add comments for documentation
COMMENT ON TABLE user_permissions IS 'User-specific permission overrides (NULL = inherit from role)';
COMMENT ON COLUMN user_permissions.user_permission_id IS 'Primary key for user permissions';
COMMENT ON COLUMN user_permissions.user_id IS 'Foreign key to users table';
COMMENT ON COLUMN user_permissions.form_name IS 'Name of the form';
COMMENT ON COLUMN user_permissions.read_permission IS 'User-specific read permission override';
COMMENT ON COLUMN user_permissions.new_permission IS 'User-specific new permission override';
COMMENT ON COLUMN user_permissions.edit_permission IS 'User-specific edit permission override';
COMMENT ON COLUMN user_permissions.delete_permission IS 'User-specific delete permission override';
COMMENT ON COLUMN user_permissions.print_permission IS 'User-specific print permission override';
-- [End] --

-- [CreateGlobalPermissionsTable] --
-- Create global_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS global_permissions (
    global_permission_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    can_create_users BOOLEAN DEFAULT FALSE,
    can_edit_users BOOLEAN DEFAULT FALSE,
    can_delete_users BOOLEAN DEFAULT FALSE,
    can_print_users BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id)
);

-- Add comments for documentation
COMMENT ON TABLE global_permissions IS 'Global user management permissions';
COMMENT ON COLUMN global_permissions.global_permission_id IS 'Primary key for global permissions';
COMMENT ON COLUMN global_permissions.user_id IS 'Foreign key to users table';
COMMENT ON COLUMN global_permissions.can_create_users IS 'Can create new users';
COMMENT ON COLUMN global_permissions.can_edit_users IS 'Can edit existing users';
COMMENT ON COLUMN global_permissions.can_delete_users IS 'Can delete users';
COMMENT ON COLUMN global_permissions.can_print_users IS 'Can print user reports';
-- [End] --

-- [AddRoleIdToUsers] --
-- Add role_id column to users table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'role_id'
    ) THEN
        ALTER TABLE users ADD COLUMN role_id INTEGER;
        ALTER TABLE users ADD CONSTRAINT FK_users_roles 
            FOREIGN KEY (role_id) REFERENCES roles(role_id);
        
        -- Add comment for documentation
        COMMENT ON COLUMN users.role_id IS 'Foreign key to roles table';
    END IF;
END $$;
-- [End] --

-- [VerifyTablesExist] --
-- Verify all RBAC tables exist
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('roles', 'role_permissions', 'user_permissions', 'global_permissions') 
        THEN 'RBAC Table'
        ELSE 'Other Table'
    END as table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('roles', 'role_permissions', 'user_permissions', 'global_permissions', 'users')
ORDER BY table_name;
-- [End] --

-- [VerifyForeignKeys] --
-- Verify foreign key constraints exist
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
AND tc.table_schema = 'public'
AND tc.table_name IN ('role_permissions', 'user_permissions', 'global_permissions', 'users')
ORDER BY tc.table_name, tc.constraint_name;
-- [End] --
