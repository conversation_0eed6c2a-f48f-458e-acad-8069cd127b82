using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models
{
    #region User Permission Update Models

    /// <summary>
    /// Model for updating user-specific permission overrides
    /// </summary>
    public class UserPermissionUpdate
    {
        /// <summary>
        /// User ID to update permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Form name to update permissions for
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FormName { get; set; }

        /// <summary>
        /// Read permission override (NULL = inherit from role)
        /// </summary>
        public bool? ReadPermission { get; set; }

        /// <summary>
        /// New record permission override (NULL = inherit from role)
        /// </summary>
        public bool? NewPermission { get; set; }

        /// <summary>
        /// Edit permission override (NULL = inherit from role)
        /// </summary>
        public bool? EditPermission { get; set; }

        /// <summary>
        /// Delete permission override (NULL = inherit from role)
        /// </summary>
        public bool? DeletePermission { get; set; }

        /// <summary>
        /// Print permission override (NULL = inherit from role)
        /// </summary>
        public bool? PrintPermission { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public UserPermissionUpdate()
        {
        }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="read">Read permission override</param>
        /// <param name="create">New permission override</param>
        /// <param name="edit">Edit permission override</param>
        /// <param name="delete">Delete permission override</param>
        /// <param name="print">Print permission override</param>
        public UserPermissionUpdate(int userId, string formName, bool? read, bool? create, bool? edit, bool? delete, bool? print)
        {
            UserId = userId;
            FormName = formName;
            ReadPermission = read;
            NewPermission = create;
            EditPermission = edit;
            DeletePermission = delete;
            PrintPermission = print;
        }

        /// <summary>
        /// Validates the update model
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return UserId > 0 && !string.IsNullOrWhiteSpace(FormName);
        }

        /// <summary>
        /// Checks if any permission is set (not all NULL)
        /// </summary>
        /// <returns>True if at least one permission is set, false if all are NULL</returns>
        public bool HasAnyPermissionSet()
        {
            return ReadPermission.HasValue ||
                   NewPermission.HasValue ||
                   EditPermission.HasValue ||
                   DeletePermission.HasValue ||
                   PrintPermission.HasValue;
        }

        /// <summary>
        /// Resets all permissions to NULL (inherit from role)
        /// </summary>
        public void ResetToRoleInheritance()
        {
            ReadPermission = null;
            NewPermission = null;
            EditPermission = null;
            DeletePermission = null;
            PrintPermission = null;
        }
    }

    /// <summary>
    /// Model for updating global permissions for user management
    /// </summary>
    public class GlobalPermissionUpdate
    {
        /// <summary>
        /// User ID to update global permissions for
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Permission to create new users
        /// </summary>
        public bool CanCreateUsers { get; set; }

        /// <summary>
        /// Permission to edit existing users
        /// </summary>
        public bool CanEditUsers { get; set; }

        /// <summary>
        /// Permission to delete users
        /// </summary>
        public bool CanDeleteUsers { get; set; }

        /// <summary>
        /// Permission to print user reports
        /// </summary>
        public bool CanPrintUsers { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public GlobalPermissionUpdate()
        {
        }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="canCreate">Can create users</param>
        /// <param name="canEdit">Can edit users</param>
        /// <param name="canDelete">Can delete users</param>
        /// <param name="canPrint">Can print user reports</param>
        public GlobalPermissionUpdate(int userId, bool canCreate, bool canEdit, bool canDelete, bool canPrint)
        {
            UserId = userId;
            CanCreateUsers = canCreate;
            CanEditUsers = canEdit;
            CanDeleteUsers = canDelete;
            CanPrintUsers = canPrint;
        }

        /// <summary>
        /// Validates the update model
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return UserId > 0;
        }

        /// <summary>
        /// Checks if any global permission is granted
        /// </summary>
        /// <returns>True if at least one global permission is true</returns>
        public bool HasAnyGlobalPermission()
        {
            return CanCreateUsers || CanEditUsers || CanDeleteUsers || CanPrintUsers;
        }

        /// <summary>
        /// Grants all global permissions
        /// </summary>
        public void GrantAllPermissions()
        {
            CanCreateUsers = true;
            CanEditUsers = true;
            CanDeleteUsers = true;
            CanPrintUsers = true;
        }

        /// <summary>
        /// Revokes all global permissions
        /// </summary>
        public void RevokeAllPermissions()
        {
            CanCreateUsers = false;
            CanEditUsers = false;
            CanDeleteUsers = false;
            CanPrintUsers = false;
        }
    }

    #endregion
}
