using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for automatically discovering forms in the MainForms folder and synchronizing
    /// them with the permission system database. Ensures the permission system stays current
    /// with available forms in the application.
    /// </summary>
    public static class FormDiscoveryService
    {
        private static readonly string MainFormsPath = Path.Combine(Application.StartupPath, "Forms", "MainForms");

        /// <summary>
        /// Scan MainForms folder and synchronize with database permission system
        /// </summary>
        /// <returns>FormSyncResult containing details of the synchronization operation</returns>
        public static FormSyncResult SyncFormsWithDatabase()
        {
            var result = new FormSyncResult();

            try
            {
                // 1. Get forms from file system
                var fileSystemForms = GetFormsFromFileSystem();
                if (fileSystemForms == null)
                {
                    result.Errors.Add("Could not access MainForms directory");
                    return result;
                }

                // 2. Get forms from database
                var databaseForms = GetFormsFromDatabase();

                // 3. Find new forms (in filesystem but not in database)
                var newForms = fileSystemForms.Except(databaseForms, StringComparer.OrdinalIgnoreCase).ToList();

                // 4. Find removed forms (in database but not in filesystem)
                var removedForms = databaseForms.Except(fileSystemForms, StringComparer.OrdinalIgnoreCase).ToList();

                // 5. Add new forms to permission system
                foreach (var formName in newForms)
                {
                    if (AddFormToPermissionSystem(formName))
                    {
                        result.FormsAdded.Add(formName);
                    }
                    else
                    {
                        result.Errors.Add($"Failed to add form: {formName}");
                    }
                }

                // 6. Remove deleted forms from permission system
                foreach (var formName in removedForms)
                {
                    if (RemoveFormFromPermissionSystem(formName))
                    {
                        result.FormsRemoved.Add(formName);
                    }
                    else
                    {
                        result.Errors.Add($"Failed to remove form: {formName}");
                    }
                }

                // 7. Update forms configuration
                UpdateFormsConfiguration(result.FormsAdded, result.FormsRemoved);

                result.Success = result.Errors.Count == 0;
                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Sync operation failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Get list of form files from MainForms directory
        /// </summary>
        /// <returns>List of form names or null if directory not accessible</returns>
        public static List<string> GetFormsFromFileSystem()
        {
            try
            {
                if (!Directory.Exists(MainFormsPath))
                {
                    return null;
                }

                return Directory.GetFiles(MainFormsPath, "*.cs")
                    .Where(f => !f.EndsWith(".Designer.cs", StringComparison.OrdinalIgnoreCase))
                    .Where(f => !f.EndsWith(".resx", StringComparison.OrdinalIgnoreCase))
                    .Select(f => Path.GetFileNameWithoutExtension(f))
                    .Where(f => !string.IsNullOrEmpty(f))
                    .OrderBy(f => f)
                    .ToList();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Get list of forms from database permission system
        /// </summary>
        /// <returns>List of form names from the database</returns>
        public static List<string> GetFormsFromDatabase()
        {
            try
            {
                // Get distinct form names from role_permissions table
                var allRoles = PermissionDatabaseService.GetAllRoles();
                var formNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                foreach (var role in allRoles)
                {
                    var permissions = PermissionDatabaseService.GetRolePermissions(role.RoleId);
                    foreach (var permission in permissions)
                    {
                        formNames.Add(permission.FormName);
                    }
                }

                return formNames.OrderBy(f => f).ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// Add new form to permission system for all roles
        /// </summary>
        /// <param name="formName">Name of the form to add</param>
        /// <returns>True if successful, false otherwise</returns>
        private static bool AddFormToPermissionSystem(string formName)
        {
            try
            {
                // Add to database with default permissions (false for all)
                var success = PermissionDatabaseService.AddFormToPermissionSystem(formName);

                if (success)
                {
                    // Log the addition
                    System.Diagnostics.Debug.WriteLine($"Added form to permission system: {formName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding form {formName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove form from permission system
        /// </summary>
        /// <param name="formName">Name of the form to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        private static bool RemoveFormFromPermissionSystem(string formName)
        {
            try
            {
                // Remove from database (both role_permissions and user_permissions)
                var success = PermissionDatabaseService.RemoveFormFromPermissionSystem(formName);

                if (success)
                {
                    // Log the removal
                    System.Diagnostics.Debug.WriteLine($"Removed form from permission system: {formName}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing form {formName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update forms configuration file with changes
        /// </summary>
        /// <param name="addedForms">List of forms that were added</param>
        /// <param name="removedForms">List of forms that were removed</param>
        private static void UpdateFormsConfiguration(List<string> addedForms, List<string> removedForms)
        {
            try
            {
                // Add new forms to configuration
                foreach (var formName in addedForms)
                {
                    var config = new FormConfiguration
                    {
                        FormName = formName,
                        DisplayName = GetDisplayNameFromFormName(formName),
                        Category = GetCategoryFromFormName(formName),
                        IsActive = true,
                        SortOrder = GetNextSortOrder()
                    };

                    FormsConfigurationService.AddFormConfiguration(config);
                }

                // Remove deleted forms from configuration
                foreach (var formName in removedForms)
                {
                    FormsConfigurationService.RemoveFormConfiguration(formName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating forms configuration: {ex.Message}");
            }
        }

        /// <summary>
        /// Generate display name from form name
        /// </summary>
        /// <param name="formName">Form name to convert</param>
        /// <returns>Human-readable display name</returns>
        private static string GetDisplayNameFromFormName(string formName)
        {
            // Convert PascalCase to readable format
            // Example: "UserMasterForm" -> "User Master"
            var result = formName.Replace("Form", "");

            // Add spaces before capital letters
            for (int i = result.Length - 1; i > 0; i--)
            {
                if (char.IsUpper(result[i]) && !char.IsUpper(result[i - 1]))
                {
                    result = result.Insert(i, " ");
                }
            }

            return result;
        }

        /// <summary>
        /// Determine category from form name
        /// </summary>
        /// <param name="formName">Form name to categorize</param>
        /// <returns>Category name</returns>
        private static string GetCategoryFromFormName(string formName)
        {
            var lowerName = formName.ToLower();

            if (lowerName.Contains("user") || lowerName.Contains("role") || lowerName.Contains("permission"))
                return "Security";

            if (lowerName.Contains("database") || lowerName.Contains("sql") || lowerName.Contains("parameter"))
                return "System";

            return "Business";
        }

        /// <summary>
        /// Get next sort order for new forms
        /// </summary>
        /// <returns>Next available sort order</returns>
        private static int GetNextSortOrder()
        {
            var allForms = FormsConfigurationService.GetAllForms();
            return allForms.Count > 0 ? allForms.Max(f => f.SortOrder) + 10 : 10;
        }

        /// <summary>
        /// Validate form file exists and is valid
        /// </summary>
        /// <param name="formName">Name of the form to validate</param>
        /// <returns>True if form files exist, false otherwise</returns>
        public static bool ValidateFormFile(string formName)
        {
            try
            {
                var formPath = Path.Combine(MainFormsPath, $"{formName}.cs");
                var designerPath = Path.Combine(MainFormsPath, $"{formName}.Designer.cs");

                return File.Exists(formPath) && File.Exists(designerPath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get sync status information
        /// </summary>
        /// <returns>Status string describing current sync state</returns>
        public static string GetSyncStatus()
        {
            try
            {
                var fileSystemForms = GetFormsFromFileSystem();
                var databaseForms = GetFormsFromDatabase();

                if (fileSystemForms == null)
                    return "Error: Cannot access MainForms directory";

                var inSync = fileSystemForms.Count == databaseForms.Count &&
                           fileSystemForms.All(f => databaseForms.Contains(f, StringComparer.OrdinalIgnoreCase));

                return inSync
                    ? $"In sync: {fileSystemForms.Count} forms"
                    : $"Out of sync: {fileSystemForms.Count} files, {databaseForms.Count} database entries";
            }
            catch (Exception ex)
            {
                return $"Error checking sync status: {ex.Message}";
            }
        }
    }
}
