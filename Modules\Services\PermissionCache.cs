using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Thread-safe cache for permission results to improve performance.
    /// Implements automatic expiration and selective cache invalidation.
    /// </summary>
    public class PermissionCache
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new ConcurrentDictionary<string, CacheItem>();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(15);

        /// <summary>
        /// Try to get permission from cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="permission">Output permission value</param>
        /// <returns>True if found in cache and not expired, false otherwise</returns>
        public bool TryGetPermission(string key, out bool permission)
        {
            permission = false;

            if (_cache.TryGetValue(key, out CacheItem item))
            {
                if (DateTime.Now - item.Timestamp < _cacheExpiry)
                {
                    permission = item.Value;
                    return true;
                }
                else
                {
                    // Remove expired item
                    _cache.TryRemove(key, out _);
                }
            }

            return false;
        }

        /// <summary>
        /// Set permission in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="permission">Permission value to cache</param>
        public void SetPermission(string key, bool permission)
        {
            _cache[key] = new CacheItem { Value = permission, Timestamp = DateTime.Now };
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        public void ClearAll()
        {
            _cache.Clear();
        }

        /// <summary>
        /// Clear cache for specific user
        /// </summary>
        /// <param name="userId">User ID to clear cache for</param>
        public void ClearUserPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// Clear cache for specific role (affects all users with that role)
        /// </summary>
        /// <param name="roleId">Role ID to clear cache for</param>
        public void ClearRolePermissions(int roleId)
        {
            // For role changes, clear all cache since we don't track role-user mapping in cache
            ClearAll();
        }

        /// <summary>
        /// Clear global permissions for user
        /// </summary>
        /// <param name="userId">User ID to clear global permissions for</param>
        public void ClearGlobalPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"global_{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// Clean expired cache entries
        /// </summary>
        public void CleanExpiredEntries()
        {
            var expiredKeys = _cache
                .Where(kvp => DateTime.Now - kvp.Value.Timestamp >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics information</returns>
        public CacheStatistics GetStatistics()
        {
            var now = DateTime.Now;
            var validEntries = _cache.Values.Count(item => now - item.Timestamp < _cacheExpiry);
            var expiredEntries = _cache.Count - validEntries;

            return new CacheStatistics
            {
                TotalEntries = _cache.Count,
                ValidEntries = validEntries,
                ExpiredEntries = expiredEntries,
                CacheExpiryMinutes = (int)_cacheExpiry.TotalMinutes
            };
        }

        /// <summary>
        /// Internal cache item structure
        /// </summary>
        private class CacheItem
        {
            public bool Value { get; set; }
            public DateTime Timestamp { get; set; }
        }
    }

    /// <summary>
    /// Cache statistics information
    /// </summary>
    public class CacheStatistics
    {
        public int TotalEntries { get; set; }
        public int ValidEntries { get; set; }
        public int ExpiredEntries { get; set; }
        public int CacheExpiryMinutes { get; set; }

        public override string ToString()
        {
            return $"Cache: {ValidEntries} valid, {ExpiredEntries} expired, {TotalEntries} total entries. Expiry: {CacheExpiryMinutes} minutes.";
        }
    }
}
