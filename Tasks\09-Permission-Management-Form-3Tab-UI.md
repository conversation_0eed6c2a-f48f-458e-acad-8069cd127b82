# Task 09: Permission Management Form (3-Tab UI)

## Objective
Create a comprehensive permission management form with 3 tabs: Role Permissions, User Permissions, and Global User Management. This form serves as the central interface for managing all aspects of the RBAC system.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-08

## Estimated Time
3 hours

## Dependencies
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 06: Core Permission Service Logic
- Task 07: Permission Database Operations

## Files to Create
- `Forms/MainForms/PermissionManagementForm.cs`
- `Forms/MainForms/PermissionManagementForm.Designer.cs`
- `Forms/MainForms/PermissionManagementForm.resx`
- `Modules/Helpers/PermissionManagementForm/PermissionGridHelper.cs`
- `Modules/Data/PermissionManagementForm/PermissionFormData.cs`

## Form Design Structure

### Main Form Layout
```
┌─────────────────────────────────────────────────────────────┐
│ Permission Management                                    [X] │
├─────────────────────────────────────────────────────────────┤
│ [Role Permissions] [User Permissions] [Global Permissions] │
├─────────────────────────────────────────────────────────────┤
│ Tab Content Area                                            │
│                                                             │
│                                                             │
│                                                             │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    [Save] [Cancel] [Refresh]               │
└─────────────────────────────────────────────────────────────┘
```

### Tab 1: Role Permissions
```
┌─────────────────────────────────────────────────────────────┐
│ Role: [Dropdown: Administrator ▼]                          │
├─────────────────────────────────────────────────────────────┤
│ Form Name          │ Read │ New │ Edit │ Delete │ Print     │
├────────────────────┼──────┼─────┼──────┼────────┼───────────┤
│ DatabaseForm       │  ☑   │  ☑  │  ☑   │   ☑    │    ☑      │
│ ParametersForm     │  ☑   │  ☑  │  ☑   │   ☑    │    ☑      │
│ UserMasterForm     │  ☑   │  ☑  │  ☑   │   ☑    │    ☑      │
└─────────────────────────────────────────────────────────────┘
```

### Tab 2: User Permissions
```
┌─────────────────────────────────────────────────────────────┐
│ User: [Dropdown: John Doe (Administrator) ▼]              │
├─────────────────────────────────────────────────────────────┤
│ Form Name      │ Read │ New │ Edit │ Delete │ Print │ Source │
├────────────────┼──────┼─────┼──────┼────────┼───────┼────────┤
│ DatabaseForm   │  ☑   │  ☑  │  ☑   │   ☑    │   ☑   │ Role   │
│ ParametersForm │  ☑   │  ☐  │  ☐   │   ☐    │   ☑   │ User   │
└─────────────────────────────────────────────────────────────┘
```

### Tab 3: Global Permissions
```
┌─────────────────────────────────────────────────────────────┐
│ User: [Dropdown: John Doe ▼]                               │
├─────────────────────────────────────────────────────────────┤
│ Global User Management Permissions:                         │
│                                                             │
│ ☑ Can Create Users                                          │
│ ☑ Can Edit Users                                            │
│ ☐ Can Delete Users                                          │
│ ☑ Can Print User Reports                                    │
└─────────────────────────────────────────────────────────────┘
```

## Implementation

### PermissionManagementForm.cs
```csharp
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraTab;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Helpers.PermissionManagementForm;
using ProManage.Modules.Data.PermissionManagementForm;

namespace ProManage.Forms.MainForms
{
    public partial class PermissionManagementForm : Form
    {
        private readonly PermissionFormData _formData;
        private readonly PermissionGridHelper _gridHelper;
        private bool _isLoading = false;
        
        public PermissionManagementForm()
        {
            InitializeComponent();
            _formData = new PermissionFormData();
            _gridHelper = new PermissionGridHelper();
            
            InitializeForm();
            LoadInitialData();
        }
        
        #region Form Initialization
        
        private void InitializeForm()
        {
            this.Text = "Permission Management";
            this.Size = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // Set MDI parent if available
            if (Application.OpenForms["MainFrame"] != null)
            {
                this.MdiParent = Application.OpenForms["MainFrame"];
                this.WindowState = FormWindowState.Maximized;
                this.FormBorderStyle = FormBorderStyle.Sizable;
            }
            
            SetupTabControl();
            SetupRolePermissionsTab();
            SetupUserPermissionsTab();
            SetupGlobalPermissionsTab();
            SetupButtons();
        }
        
        private void SetupTabControl()
        {
            tabControlMain.SelectedPageChanged += TabControlMain_SelectedPageChanged;
        }
        
        #endregion
        
        #region Tab 1: Role Permissions
        
        private void SetupRolePermissionsTab()
        {
            // Setup role dropdown
            cmbRoles.Properties.DisplayMember = "RoleName";
            cmbRoles.Properties.ValueMember = "RoleId";
            cmbRoles.EditValueChanged += CmbRoles_EditValueChanged;
            
            // Setup role permissions grid
            _gridHelper.SetupRolePermissionsGrid(gridControlRolePermissions, gridViewRolePermissions);
            gridViewRolePermissions.CellValueChanged += GridViewRolePermissions_CellValueChanged;
        }
        
        private void LoadRolePermissions()
        {
            if (_isLoading || cmbRoles.EditValue == null) return;
            
            try
            {
                _isLoading = true;
                var roleId = (int)cmbRoles.EditValue;
                var permissions = _formData.GetRolePermissions(roleId);
                
                gridControlRolePermissions.DataSource = permissions;
                gridViewRolePermissions.BestFitColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        private void CmbRoles_EditValueChanged(object sender, EventArgs e)
        {
            LoadRolePermissions();
        }
        
        private void GridViewRolePermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (_isLoading) return;
            
            // Mark form as dirty
            btnSave.Enabled = true;
        }
        
        #endregion
        
        #region Tab 2: User Permissions
        
        private void SetupUserPermissionsTab()
        {
            // Setup user dropdown
            cmbUsers.Properties.DisplayMember = "DisplayName";
            cmbUsers.Properties.ValueMember = "UserId";
            cmbUsers.EditValueChanged += CmbUsers_EditValueChanged;
            
            // Setup user permissions grid
            _gridHelper.SetupUserPermissionsGrid(gridControlUserPermissions, gridViewUserPermissions);
            gridViewUserPermissions.CellValueChanged += GridViewUserPermissions_CellValueChanged;
            
            // Setup reset button
            btnResetUserPermissions.Click += BtnResetUserPermissions_Click;
        }
        
        private void LoadUserPermissions()
        {
            if (_isLoading || cmbUsers.EditValue == null) return;
            
            try
            {
                _isLoading = true;
                var userId = (int)cmbUsers.EditValue;
                var permissions = _formData.GetUserEffectivePermissions(userId);
                
                gridControlUserPermissions.DataSource = permissions;
                gridViewUserPermissions.BestFitColumns();
                
                // Color code the grid based on permission source
                _gridHelper.ApplyUserPermissionFormatting(gridViewUserPermissions);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        private void CmbUsers_EditValueChanged(object sender, EventArgs e)
        {
            LoadUserPermissions();
            LoadGlobalPermissions();
        }
        
        private void GridViewUserPermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (_isLoading) return;
            
            btnSave.Enabled = true;
        }
        
        private void BtnResetUserPermissions_Click(object sender, EventArgs e)
        {
            if (cmbUsers.EditValue == null) return;
            
            var result = MessageBox.Show(
                "This will remove all user permission overrides and revert to role permissions. Continue?",
                "Confirm Reset",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                var userId = (int)cmbUsers.EditValue;
                if (_formData.ResetUserPermissions(userId))
                {
                    LoadUserPermissions();
                    MessageBox.Show("User permissions reset successfully.", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Failed to reset user permissions.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
        
        #endregion
        
        #region Tab 3: Global Permissions
        
        private void SetupGlobalPermissionsTab()
        {
            // Setup user dropdown (same as tab 2)
            cmbGlobalUsers.Properties.DisplayMember = "DisplayName";
            cmbGlobalUsers.Properties.ValueMember = "UserId";
            cmbGlobalUsers.EditValueChanged += CmbGlobalUsers_EditValueChanged;
            
            // Setup global permission checkboxes
            chkCanCreateUsers.CheckedChanged += GlobalPermission_CheckedChanged;
            chkCanEditUsers.CheckedChanged += GlobalPermission_CheckedChanged;
            chkCanDeleteUsers.CheckedChanged += GlobalPermission_CheckedChanged;
            chkCanPrintUsers.CheckedChanged += GlobalPermission_CheckedChanged;
        }
        
        private void LoadGlobalPermissions()
        {
            if (_isLoading || cmbGlobalUsers.EditValue == null) return;
            
            try
            {
                _isLoading = true;
                var userId = (int)cmbGlobalUsers.EditValue;
                var globalPermissions = _formData.GetGlobalPermissions(userId);
                
                if (globalPermissions != null)
                {
                    chkCanCreateUsers.Checked = globalPermissions.CanCreateUsers;
                    chkCanEditUsers.Checked = globalPermissions.CanEditUsers;
                    chkCanDeleteUsers.Checked = globalPermissions.CanDeleteUsers;
                    chkCanPrintUsers.Checked = globalPermissions.CanPrintUsers;
                }
                else
                {
                    // No global permissions set - default to false
                    chkCanCreateUsers.Checked = false;
                    chkCanEditUsers.Checked = false;
                    chkCanDeleteUsers.Checked = false;
                    chkCanPrintUsers.Checked = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading global permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        private void CmbGlobalUsers_EditValueChanged(object sender, EventArgs e)
        {
            LoadGlobalPermissions();
        }
        
        private void GlobalPermission_CheckedChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;
            
            btnSave.Enabled = true;
        }
        
        #endregion
        
        #region Form Events
        
        private void TabControlMain_SelectedPageChanged(object sender, TabPageChangedEventArgs e)
        {
            // Sync user selection between tabs 2 and 3
            if (e.Page == tabPageUserPermissions && cmbGlobalUsers.EditValue != null)
            {
                cmbUsers.EditValue = cmbGlobalUsers.EditValue;
            }
            else if (e.Page == tabPageGlobalPermissions && cmbUsers.EditValue != null)
            {
                cmbGlobalUsers.EditValue = cmbUsers.EditValue;
            }
        }
        
        private void LoadInitialData()
        {
            try
            {
                _isLoading = true;
                
                // Load roles
                var roles = _formData.GetAllRoles();
                cmbRoles.Properties.DataSource = roles;
                if (roles.Count > 0)
                {
                    cmbRoles.EditValue = roles[0].RoleId;
                }
                
                // Load users
                var users = _formData.GetAllUsers();
                cmbUsers.Properties.DataSource = users;
                cmbGlobalUsers.Properties.DataSource = users;
                
                if (users.Count > 0)
                {
                    cmbUsers.EditValue = users[0].UserId;
                    cmbGlobalUsers.EditValue = users[0].UserId;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading initial data: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }
        
        #endregion
        
        #region Button Events
        
        private void SetupButtons()
        {
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            btnSave.Enabled = false; // Enable only when changes are made
        }
        
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                var success = true;
                
                // Save based on current tab
                switch (tabControlMain.SelectedTabPageIndex)
                {
                    case 0: // Role Permissions
                        success = SaveRolePermissions();
                        break;
                    case 1: // User Permissions
                        success = SaveUserPermissions();
                        break;
                    case 2: // Global Permissions
                        success = SaveGlobalPermissions();
                        break;
                }
                
                if (success)
                {
                    btnSave.Enabled = false;
                    MessageBox.Show("Permissions saved successfully.", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // Clear permission cache
                    PermissionService.ClearPermissionCache();
                }
                else
                {
                    MessageBox.Show("Failed to save permissions.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private bool SaveRolePermissions()
        {
            if (cmbRoles.EditValue == null) return false;
            
            var roleId = (int)cmbRoles.EditValue;
            var permissions = (List<RolePermissionDisplay>)gridControlRolePermissions.DataSource;
            
            return _formData.SaveRolePermissions(roleId, permissions);
        }
        
        private bool SaveUserPermissions()
        {
            if (cmbUsers.EditValue == null) return false;
            
            var userId = (int)cmbUsers.EditValue;
            var permissions = (List<UserPermissionDisplay>)gridControlUserPermissions.DataSource;
            
            return _formData.SaveUserPermissions(userId, permissions);
        }
        
        private bool SaveGlobalPermissions()
        {
            if (cmbGlobalUsers.EditValue == null) return false;
            
            var userId = (int)cmbGlobalUsers.EditValue;
            var globalPermissions = new GlobalPermissionUpdate
            {
                UserId = userId,
                CanCreateUsers = chkCanCreateUsers.Checked,
                CanEditUsers = chkCanEditUsers.Checked,
                CanDeleteUsers = chkCanDeleteUsers.Checked,
                CanPrintUsers = chkCanPrintUsers.Checked
            };
            
            return _formData.SaveGlobalPermissions(globalPermissions);
        }
        
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (btnSave.Enabled)
            {
                var result = MessageBox.Show("You have unsaved changes. Are you sure you want to cancel?", 
                    "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.No)
                    return;
            }
            
            this.Close();
        }
        
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadInitialData();
            
            // Reload current tab data
            switch (tabControlMain.SelectedTabPageIndex)
            {
                case 0:
                    LoadRolePermissions();
                    break;
                case 1:
                    LoadUserPermissions();
                    break;
                case 2:
                    LoadGlobalPermissions();
                    break;
            }
            
            btnSave.Enabled = false;
        }
        
        #endregion
    }
}
```

## Helper Classes

### PermissionGridHelper.cs
```csharp
using System.Drawing;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using ProManage.Modules.Models;

namespace ProManage.Modules.Helpers.PermissionManagementForm
{
    public class PermissionGridHelper
    {
        public void SetupRolePermissionsGrid(GridControl gridControl, GridView gridView)
        {
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsSelection.EnableAppearanceFocusedRow = false;
            
            // Setup columns
            var colFormName = new GridColumn
            {
                FieldName = "FormName",
                Caption = "Form Name",
                Width = 200,
                OptionsColumn = { AllowEdit = false }
            };
            
            var colRead = new GridColumn
            {
                FieldName = "ReadPermission",
                Caption = "Read",
                Width = 60,
                UnboundType = DevExpress.Data.UnboundColumnType.Boolean
            };
            
            var colNew = new GridColumn
            {
                FieldName = "NewPermission",
                Caption = "New",
                Width = 60,
                UnboundType = DevExpress.Data.UnboundColumnType.Boolean
            };
            
            var colEdit = new GridColumn
            {
                FieldName = "EditPermission",
                Caption = "Edit",
                Width = 60,
                UnboundType = DevExpress.Data.UnboundColumnType.Boolean
            };
            
            var colDelete = new GridColumn
            {
                FieldName = "DeletePermission",
                Caption = "Delete",
                Width = 60,
                UnboundType = DevExpress.Data.UnboundColumnType.Boolean
            };
            
            var colPrint = new GridColumn
            {
                FieldName = "PrintPermission",
                Caption = "Print",
                Width = 60,
                UnboundType = DevExpress.Data.UnboundColumnType.Boolean
            };
            
            gridView.Columns.AddRange(new[] { colFormName, colRead, colNew, colEdit, colDelete, colPrint });
        }
        
        public void SetupUserPermissionsGrid(GridControl gridControl, GridView gridView)
        {
            SetupRolePermissionsGrid(gridControl, gridView);
            
            // Add source column
            var colSource = new GridColumn
            {
                FieldName = "Source",
                Caption = "Source",
                Width = 80,
                OptionsColumn = { AllowEdit = false }
            };
            
            gridView.Columns.Add(colSource);
        }
        
        public void ApplyUserPermissionFormatting(GridView gridView)
        {
            gridView.RowCellStyle += (sender, e) =>
            {
                if (e.Column.FieldName == "Source") return;
                
                var source = gridView.GetRowCellValue(e.RowHandle, "Source")?.ToString();
                
                if (source == "UserOverride")
                {
                    e.Appearance.BackColor = Color.LightBlue;
                    e.Appearance.ForeColor = Color.DarkBlue;
                }
                else if (source == "Role")
                {
                    e.Appearance.BackColor = Color.LightGreen;
                    e.Appearance.ForeColor = Color.DarkGreen;
                }
            };
        }
    }
}
```

## Acceptance Criteria

- [ ] 3-tab interface for role, user, and global permissions
- [ ] Role permissions grid with checkbox editing
- [ ] User permissions grid showing role vs override with color coding
- [ ] Global permissions checkboxes for user management
- [ ] Save/Cancel/Refresh functionality
- [ ] Data validation and error handling
- [ ] MDI child form integration
- [ ] Permission cache clearing after saves
- [ ] User permission reset functionality
- [ ] Tab synchronization for user selection

## Dependencies
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 06: Core Permission Service Logic
- Task 07: Permission Database Operations

## Next Tasks
This task enables:
- Task 13: MainFrame Ribbon Permission Filtering
- Task 15: Global Permission Implementation
