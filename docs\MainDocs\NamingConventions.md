# ProManage Naming Conventions

This document establishes comprehensive naming standards for the ProManage project to ensure consistency, maintainability, and clarity across all code components.

## File Naming Conventions

### Form-Specific Files Pattern: `{FormName}-{Purpose}.cs`

Form-specific files follow a strict naming pattern that clearly identifies which form they belong to and their purpose:

**Pattern**: `{FormName}-{Purpose}.cs`
**Class Name**: `{FormName}{Purpose}` (PascalCase, no hyphens)

#### Examples:

**EstimateForm-related files:**
- `EstimateForm-Repository.cs` → class `EstimateFormRepository`
- `EstimateForm-QueryService.cs` → class `EstimateFormQueryService`
- `EstimateForm-HeaderModel.cs` → class `EstimateFormHeaderModel`
- `EstimateForm-DetailModel.cs` → class `EstimateFormDetailModel`
- `EstimateForm-Helper.cs` → class `EstimateFormHelper`
- `EstimateForm-Validation.cs` → class `EstimateFormValidation`
- `EstimateForm-EventHandlers.cs` → class `EstimateFormEventHandlers`

**LoginForm-related files:**
- `LoginForm-UserManager.cs` → class `LoginFormUserManager`
- `LoginForm-UserModel.cs` → class `LoginFormUserModel`
- `LoginForm-Validation.cs` → class `LoginFormValidation`

**SQLQueryForm-related files:**
- `SQLQueryForm-Service.cs` → class `SQLQueryFormService`
- `SQLQueryForm-Helper.cs` → class `SQLQueryFormHelper`

### Shared/Common Files Pattern: Descriptive Functionality Names

Shared files that are used across multiple forms or provide general functionality use descriptive names:

**Examples:**
- `ConfigurationHelper.cs` → class `ConfigurationHelper`
- `DatabaseConnectionManager.cs` → class `DatabaseConnectionManager`
- `SQLQueryLoader.cs` → class `SQLQueryLoader`
- `ProgressIndicatorService.cs` → class `ProgressIndicatorService`
- `LicenseManager.cs` → class `LicenseManager`
- `ValidationUtilities.cs` → class `ValidationUtilities`
- `ReportManager.cs` → class `ReportManager`

### SQL Query Files

SQL files use PascalCase with descriptive names:
- `EstimateCRUD.sql`
- `EstimateNavigation.sql`
- `GetAllTables.sql`
- `ValidateUser.sql`

## Class Naming Conventions

### Rule: Class Names Must Match File Names

**File Name → Class Name Conversion:**
1. Remove the `.cs` extension
2. Convert hyphens to PascalCase
3. Ensure the class name exactly matches the converted file name

**Examples:**
- `EstimateForm-Repository.cs` → `EstimateFormRepository`
- `LoginForm-UserManager.cs` → `LoginFormUserManager`
- `ConfigurationHelper.cs` → `ConfigurationHelper`

### Class Types and Naming Patterns

**Repository Classes:**
- Pattern: `{FormName}Repository` or `{Entity}Repository`
- Examples: `EstimateFormRepository`, `UserRepository`

**Service Classes:**
- Pattern: `{FormName}Service` or `{Functionality}Service`
- Examples: `EstimateFormQueryService`, `ProgressIndicatorService`

**Model Classes:**
- Pattern: `{FormName}{ModelType}Model` or `{Entity}Model`
- Examples: `EstimateFormHeaderModel`, `UserModel`

**Helper Classes:**
- Pattern: `{Functionality}Helper` or `{FormName}Helper`
- Examples: `ConfigurationHelper`, `EstimateFormHelper`

**Manager Classes:**
- Pattern: `{Functionality}Manager`
- Examples: `DatabaseConnectionManager`, `LicenseManager`

## Namespace Organization

### Namespace Structure Rules

**Root Namespace:** `ProManage`

**Module Namespaces:** `ProManage.Modules.{SubfolderName}`

**Examples:**
- `ProManage.Modules.Data`
- `ProManage.Modules.Models`
- `ProManage.Modules.Helpers`
- `ProManage.Modules.Connections`
- `ProManage.Modules.UI`
- `ProManage.Modules.Licensing`
- `ProManage.Modules.Reports`

**Form Namespaces:** `ProManage.Forms`

**Report Namespaces:** `ProManage.Reports`

**Service Namespaces:** `ProManage.Services` (separate project)

### Namespace-to-Folder Mapping

The namespace must exactly match the folder structure:
- File: `Modules/Data/EstimateForm-Repository.cs`
- Namespace: `ProManage.Modules.Data`
- Class: `EstimateFormRepository`

## Variable and Property Naming

### Properties (Public)
**Pattern:** PascalCase with noun-based naming
```csharp
public string CustomerName { get; set; }
public DateTime? DocDate { get; set; }
public int EstimateId { get; set; }
```

### Private Fields
**Pattern:** _camelCase (underscore prefix)
```csharp
private string _connectionString;
private bool _isInitialized;
private EstimateFormHeaderModel _currentEstimate;
```

### Local Variables and Parameters
**Pattern:** camelCase
```csharp
public void SaveEstimate(EstimateFormHeaderModel estimate)
{
    string connectionString = GetConnectionString();
    bool isNewRecord = estimate.Id <= 0;
}
```

### Constants
**Pattern:** ALL_CAPS with underscores
```csharp
public const string DEFAULT_CONNECTION_STRING = "DefaultConnection";
public const int MAX_RETRY_ATTEMPTS = 3;
```

## Method Naming Conventions

### Method Patterns
**Pattern:** PascalCase with verb-first naming

**CRUD Operations:**
- `GetEstimateById(int id)`
- `SaveEstimate(EstimateFormHeaderModel estimate)`
- `DeleteEstimate(int id)`
- `UpdateEstimate(EstimateFormHeaderModel estimate)`

**Query Operations:**
- `SearchEstimatesByCustomer(string customerName)`
- `GetAllEstimates()`
- `FindUserByUsername(string username)`

**Validation Operations:**
- `ValidateEstimateData(EstimateFormHeaderModel estimate)`
- `IsValidUser(string username, string password)`

**Utility Operations:**
- `LoadConfiguration()`
- `InitializeDatabase()`
- `ShowProgressIndicator()`

## Control Naming (Forms)

### Control Prefixes
- `txt` - TextBox controls
- `cbo` - ComboBox controls
- `chk` - CheckBox controls
- `btn` - Button controls
- `dtp` - DateTimePicker controls
- `grid` - Grid controls
- `lbl` - Label controls

**Examples:**
```csharp
private TextBox txtCustomerName;
private ComboBox cboStatus;
private Button btnSave;
private DateTimePicker dtpEstimateDate;
private GridControl gridEstimateDetails;
```

## Step-by-Step Guide for Developers

### Adding a New Form-Specific File

1. **Determine the form name** (e.g., "CustomerForm")
2. **Determine the purpose** (e.g., "Repository", "Service", "Model")
3. **Create the file name**: `CustomerForm-Repository.cs`
4. **Create the class name**: `CustomerFormRepository`
5. **Set the namespace**: `ProManage.Modules.Data` (based on folder)
6. **Place in appropriate folder**: `Modules/Data/`

### Adding a New Shared/Common File

1. **Determine the functionality** (e.g., "Email", "Logging")
2. **Create descriptive name**: `EmailService.cs` or `LoggingHelper.cs`
3. **Create matching class name**: `EmailService` or `LoggingHelper`
4. **Set appropriate namespace** based on folder location
5. **Place in appropriate folder** (`Helpers/`, `Services/`, etc.)

### Renaming Existing Files

1. **Rename the file** following the conventions
2. **Update the class name** to match the new file name
3. **Update all using statements** that reference the class
4. **Update project file references** if necessary
5. **Test compilation** to ensure no broken references

## Common Mistakes to Avoid

### ❌ Incorrect Examples:
- `estimateRepository.cs` (wrong casing)
- `Estimate_Repository.cs` (underscores instead of hyphens)
- `EstimateFormRepository.cs` (missing hyphen in file name)
- `estimate-form-repository.cs` (all lowercase)

### ✅ Correct Examples:
- `EstimateForm-Repository.cs` with class `EstimateFormRepository`
- `ConfigurationHelper.cs` with class `ConfigurationHelper`
- `LoginForm-UserModel.cs` with class `LoginFormUserModel`

### Namespace Mistakes:
- ❌ `using ProManage.Data;` (missing Modules)
- ✅ `using ProManage.Modules.Data;`

### Class Name Mistakes:
- ❌ `class EstimateForm-Repository` (hyphens in class name)
- ✅ `class EstimateFormRepository`

## Troubleshooting Naming Issues

### Compilation Errors
1. **Check class name matches file name** (converted from hyphen to PascalCase)
2. **Verify namespace matches folder structure**
3. **Ensure using statements are correct**
4. **Check for duplicate class names** in the same namespace

### IntelliSense Issues
1. **Rebuild the solution** after renaming files
2. **Check that file is included** in the project file
3. **Verify namespace declarations** are correct

### Runtime Errors
1. **Check reflection-based code** that might reference old class names
2. **Verify configuration files** don't reference old class names
3. **Update any string-based type references**

This naming convention ensures consistency, improves code readability, and makes the codebase easier to navigate and maintain.

## Advanced Naming Scenarios

### Event Handler Naming
**Pattern:** `{ControlName}_{EventName}` or `{FormName}_{EventName}`
```csharp
private void btnSave_Click(object sender, EventArgs e)
private void EstimateForm_Load(object sender, EventArgs e)
private void gridEstimateDetails_CellValueChanged(object sender, EventArgs e)
```

### Interface Naming
**Pattern:** `I{InterfaceName}`
```csharp
public interface IEstimateRepository
public interface IUserManager
public interface IConfigurationHelper
```

### Enum Naming
**Pattern:** PascalCase for enum and values
```csharp
public enum EstimateStatus
{
    Draft,
    Pending,
    Approved,
    Rejected,
    Closed
}
```

### Generic Type Parameters
**Pattern:** Single letter starting with 'T'
```csharp
public class Repository<T> where T : class
public interface IService<TEntity, TKey>
```

## Documentation Standards

### XML Documentation Comments
Always include XML documentation for public classes and methods:

```csharp
/// <summary>
/// Repository class for handling database operations related to estimates.
/// Uses EstimateFormQueryService to execute SQL queries from the Procedures folder.
/// </summary>
public class EstimateFormRepository
{
    /// <summary>
    /// Gets an estimate by its ID
    /// </summary>
    /// <param name="id">The ID of the estimate to retrieve</param>
    /// <returns>EstimateFormHeaderModel object with details or null if not found</returns>
    public static EstimateFormHeaderModel GetEstimateById(int id)
    {
        // Implementation
    }
}
```

### File Header Comments
Include file headers for complex files:

```csharp
/*
 * File: EstimateForm-Repository.cs
 * Purpose: Data access layer for estimate management
 * Created: [Date]
 * Modified: [Date]
 * Dependencies: DatabaseConnectionManager, SQLQueryLoader
 */
```

## Validation Checklist

Before committing code, verify:

- [ ] File name follows the established pattern
- [ ] Class name matches file name (converted from hyphen to PascalCase)
- [ ] Namespace matches folder structure
- [ ] All using statements are correct
- [ ] Variable and method names follow conventions
- [ ] XML documentation is present for public members
- [ ] No naming conflicts exist
- [ ] Project compiles successfully
- [ ] All references are updated if file was renamed

## Migration Guide

### Converting from Old Naming Convention

If you encounter files that don't follow the current convention:

1. **Identify the file type** (form-specific vs. shared)
2. **Determine the correct new name** using the patterns above
3. **Rename the file** in the file system
4. **Update the class name** inside the file
5. **Update all references** throughout the codebase
6. **Update the project file** if necessary
7. **Test compilation and functionality**

### Example Migration:
```
Old: EstimateRepository.cs (class EstimateRepository)
New: EstimateForm-Repository.cs (class EstimateFormRepository)

Steps:
1. Rename file: EstimateRepository.cs → EstimateForm-Repository.cs
2. Update class: EstimateRepository → EstimateFormRepository
3. Update references: EstimateRepository → EstimateFormRepository
4. Update using statements if namespace changed
5. Test compilation
```

This comprehensive naming convention ensures long-term maintainability and consistency across the entire ProManage codebase.
