using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models
{
    #region Role Models

    /// <summary>
    /// Represents a role in the RBAC system
    /// </summary>
    public class Role
    {
        /// <summary>
        /// Primary key for the role
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Name of the role (must be unique)
        /// </summary>
        [Required(ErrorMessage = "Role name is required")]
        [StringLength(50, ErrorMessage = "Role name cannot exceed 50 characters")]
        public string RoleName { get; set; }

        /// <summary>
        /// Description of the role and its purpose
        /// </summary>
        [StringLength(255, ErrorMessage = "Description cannot exceed 255 characters")]
        public string Description { get; set; }

        /// <summary>
        /// Whether this role is active and can be assigned to users
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Date when this role was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date when this role was last modified
        /// </summary>
        public DateTime ModifiedDate { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public Role()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// Validates the role data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(RoleName) &&
                   RoleName.Length <= 50 &&
                   (string.IsNullOrEmpty(Description) || Description.Length <= 255);
        }

        /// <summary>
        /// Updates the modified date to current time
        /// </summary>
        public void UpdateModifiedDate()
        {
            ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// Creates a copy of the role for editing
        /// </summary>
        /// <returns>Copy of the role</returns>
        public Role Clone()
        {
            return new Role
            {
                RoleId = this.RoleId,
                RoleName = this.RoleName,
                Description = this.Description,
                IsActive = this.IsActive,
                CreatedDate = this.CreatedDate,
                ModifiedDate = this.ModifiedDate
            };
        }

        /// <summary>
        /// Returns a string representation of the role
        /// </summary>
        /// <returns>Role name</returns>
        public override string ToString()
        {
            return RoleName ?? string.Empty;
        }
    }

    #endregion

    #region Role Update Models

    /// <summary>
    /// Model for updating role permissions in batch operations
    /// </summary>
    public class RolePermissionUpdate
    {
        /// <summary>
        /// Role ID to update permissions for
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Form name to update permissions for
        /// </summary>
        [Required]
        [StringLength(100)]
        public string FormName { get; set; }

        /// <summary>
        /// Read permission setting
        /// </summary>
        public bool ReadPermission { get; set; }

        /// <summary>
        /// New record permission setting
        /// </summary>
        public bool NewPermission { get; set; }

        /// <summary>
        /// Edit permission setting
        /// </summary>
        public bool EditPermission { get; set; }

        /// <summary>
        /// Delete permission setting
        /// </summary>
        public bool DeletePermission { get; set; }

        /// <summary>
        /// Print permission setting
        /// </summary>
        public bool PrintPermission { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public RolePermissionUpdate()
        {
        }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="read">Read permission</param>
        /// <param name="create">New permission</param>
        /// <param name="edit">Edit permission</param>
        /// <param name="delete">Delete permission</param>
        /// <param name="print">Print permission</param>
        public RolePermissionUpdate(int roleId, string formName, bool read, bool create, bool edit, bool delete, bool print)
        {
            RoleId = roleId;
            FormName = formName;
            ReadPermission = read;
            NewPermission = create;
            EditPermission = edit;
            DeletePermission = delete;
            PrintPermission = print;
        }

        /// <summary>
        /// Validates the update model
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return RoleId > 0 && !string.IsNullOrWhiteSpace(FormName);
        }
    }

    #endregion
}
