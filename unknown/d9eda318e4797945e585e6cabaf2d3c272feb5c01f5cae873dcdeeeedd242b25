# Task 14: Individual Form Permission Checks

## Objective
Implement permission checks within individual forms to control access to specific functionality (New, Edit, Delete, Print buttons) and ensure forms respect the RBAC system at the granular level.

## Priority
**INTEGRATION** - Depends on Tasks 01-13

## Estimated Time
2 hours

## Dependencies
- Task 06: Core Permission Service Logic
- Task 13: MainFrame Ribbon Permission Filtering

## Files to Create/Modify
- `Modules/UI/BaseFormWithPermissions.cs` (create new base class)
- Modify existing MainForms to inherit from base class
- `Modules/Helpers/FormPermissionHelper.cs`

## Implementation Strategy

### 1. Create Base Form with Permission Support
Create a base form class that all MainForms can inherit from to get automatic permission checking.

### 2. Implement Permission Checking Pattern
Standardize how forms check and apply permissions to their controls.

### 3. Update Existing Forms
Modify existing MainForms to use the new permission system.

## Implementation

### BaseFormWithPermissions.cs
```csharp
using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraBars;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Modules.UI
{
    public partial class BaseFormWithPermissions : XtraForm
    {
        protected int CurrentUserId { get; private set; }
        protected string FormName { get; set; }
        protected bool HasReadPermission { get; private set; }
        protected bool HasNewPermission { get; private set; }
        protected bool HasEditPermission { get; private set; }
        protected bool HasDeletePermission { get; private set; }
        protected bool HasPrintPermission { get; private set; }
        
        // Common button references (forms can set these)
        protected SimpleButton btnNew;
        protected SimpleButton btnSave;
        protected SimpleButton btnDelete;
        protected SimpleButton btnPrint;
        protected SimpleButton btnRefresh;
        
        // Bar button references for ribbon forms
        protected BarButtonItem barBtnNew;
        protected BarButtonItem barBtnSave;
        protected BarButtonItem barBtnDelete;
        protected BarButtonItem barBtnPrint;
        protected BarButtonItem barBtnRefresh;
        
        public BaseFormWithPermissions()
        {
            InitializeComponent();
        }
        
        public BaseFormWithPermissions(string formName) : this()
        {
            FormName = formName;
        }
        
        #region Permission Initialization
        
        protected override void OnLoad(EventArgs e)
        {
            // Get current user
            CurrentUserId = GetCurrentUserId();
            
            // Set form name if not already set
            if (string.IsNullOrEmpty(FormName))
            {
                FormName = this.GetType().Name;
            }
            
            // Check permissions
            CheckFormPermissions();
            
            // Apply permissions to controls
            ApplyPermissionsToControls();
            
            base.OnLoad(e);
        }
        
        protected virtual void CheckFormPermissions()
        {
            try
            {
                if (CurrentUserId <= 0)
                {
                    // No user logged in - deny all access
                    HasReadPermission = false;
                    HasNewPermission = false;
                    HasEditPermission = false;
                    HasDeletePermission = false;
                    HasPrintPermission = false;
                    return;
                }
                
                // Check each permission type
                HasReadPermission = PermissionService.HasPermission(CurrentUserId, FormName, PermissionType.Read);
                HasNewPermission = PermissionService.HasPermission(CurrentUserId, FormName, PermissionType.New);
                HasEditPermission = PermissionService.HasPermission(CurrentUserId, FormName, PermissionType.Edit);
                HasDeletePermission = PermissionService.HasPermission(CurrentUserId, FormName, PermissionType.Delete);
                HasPrintPermission = PermissionService.HasPermission(CurrentUserId, FormName, PermissionType.Print);
                
                // If no read permission, close the form
                if (!HasReadPermission)
                {
                    ShowAccessDeniedMessage();
                    this.Close();
                    return;
                }
            }
            catch (Exception ex)
            {
                // Log error and deny access for security
                System.Diagnostics.Debug.WriteLine($"Error checking permissions for {FormName}: {ex.Message}");
                
                ShowPermissionErrorMessage();
                this.Close();
            }
        }
        
        protected virtual void ApplyPermissionsToControls()
        {
            // Apply permissions to simple buttons
            if (btnNew != null) btnNew.Enabled = HasNewPermission;
            if (btnSave != null) btnSave.Enabled = HasEditPermission;
            if (btnDelete != null) btnDelete.Enabled = HasDeletePermission;
            if (btnPrint != null) btnPrint.Enabled = HasPrintPermission;
            
            // Apply permissions to bar buttons
            if (barBtnNew != null) barBtnNew.Enabled = HasNewPermission;
            if (barBtnSave != null) barBtnSave.Enabled = HasEditPermission;
            if (barBtnDelete != null) barBtnDelete.Enabled = HasDeletePermission;
            if (barBtnPrint != null) barBtnPrint.Enabled = HasPrintPermission;
            
            // Apply read-only mode if no edit permission
            if (!HasEditPermission)
            {
                SetFormReadOnly();
            }
            
            // Update form title with permission indicators
            UpdateFormTitleWithPermissions();
        }
        
        #endregion
        
        #region Permission Enforcement
        
        protected virtual void SetFormReadOnly()
        {
            // Override in derived forms to implement read-only mode
            // Default implementation: disable all input controls
            SetControlsReadOnly(this);
        }
        
        private void SetControlsReadOnly(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                switch (control)
                {
                    case TextEdit textEdit:
                        textEdit.Properties.ReadOnly = true;
                        break;
                    case ComboBoxEdit comboEdit:
                        comboEdit.Properties.ReadOnly = true;
                        break;
                    case CheckEdit checkEdit:
                        checkEdit.Properties.ReadOnly = true;
                        break;
                    case DateEdit dateEdit:
                        dateEdit.Properties.ReadOnly = true;
                        break;
                    case LookUpEdit lookupEdit:
                        lookupEdit.Properties.ReadOnly = true;
                        break;
                    case MemoEdit memoEdit:
                        memoEdit.Properties.ReadOnly = true;
                        break;
                    default:
                        // Recursively process container controls
                        if (control.HasChildren)
                        {
                            SetControlsReadOnly(control);
                        }
                        break;
                }
            }
        }
        
        protected virtual void UpdateFormTitleWithPermissions()
        {
            if (string.IsNullOrEmpty(this.Text)) return;
            
            var originalTitle = this.Text.Split('[')[0].Trim(); // Remove existing permission indicators
            var indicators = new List<string>();
            
            if (!HasEditPermission) indicators.Add("Read-Only");
            if (!HasNewPermission && !HasEditPermission && !HasDeletePermission) indicators.Add("View Only");
            
            if (indicators.Count > 0)
            {
                this.Text = $"{originalTitle} [{string.Join(", ", indicators)}]";
            }
            else
            {
                this.Text = originalTitle;
            }
        }
        
        #endregion
        
        #region Permission Validation Methods
        
        protected bool ValidateNewPermission()
        {
            if (!HasNewPermission)
            {
                ShowInsufficientPermissionMessage("create new records");
                return false;
            }
            return true;
        }
        
        protected bool ValidateEditPermission()
        {
            if (!HasEditPermission)
            {
                ShowInsufficientPermissionMessage("edit records");
                return false;
            }
            return true;
        }
        
        protected bool ValidateDeletePermission()
        {
            if (!HasDeletePermission)
            {
                ShowInsufficientPermissionMessage("delete records");
                return false;
            }
            return true;
        }
        
        protected bool ValidatePrintPermission()
        {
            if (!HasPrintPermission)
            {
                ShowInsufficientPermissionMessage("print reports");
                return false;
            }
            return true;
        }
        
        #endregion
        
        #region Message Methods
        
        protected virtual void ShowAccessDeniedMessage()
        {
            var displayName = FormsConfigurationService.GetFormDisplayName(FormName);
            MessageBox.Show($"You do not have permission to access {displayName}.", 
                "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
        
        protected virtual void ShowPermissionErrorMessage()
        {
            MessageBox.Show("An error occurred while checking permissions. Access denied for security.", 
                "Permission Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        
        protected virtual void ShowInsufficientPermissionMessage(string action)
        {
            MessageBox.Show($"You do not have permission to {action}.", 
                "Insufficient Permissions", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
        
        #endregion
        
        #region Helper Methods
        
        protected virtual int GetCurrentUserId()
        {
            // Implement based on existing user session management
            // This should return the currently logged-in user ID
            return UserManager.Instance?.CurrentUser?.UserId ?? 0; // TODO: Implement based on existing patterns
        }
        
        /// <summary>
        /// Refresh permissions (call when user permissions might have changed)
        /// </summary>
        public virtual void RefreshPermissions()
        {
            CheckFormPermissions();
            ApplyPermissionsToControls();
        }
        
        /// <summary>
        /// Check if current user has specific permission
        /// </summary>
        protected bool HasPermission(PermissionType permissionType)
        {
            return permissionType switch
            {
                PermissionType.Read => HasReadPermission,
                PermissionType.New => HasNewPermission,
                PermissionType.Edit => HasEditPermission,
                PermissionType.Delete => HasDeletePermission,
                PermissionType.Print => HasPrintPermission,
                _ => false
            };
        }
        
        #endregion
        
        #region Event Handlers for Common Operations
        
        protected virtual void OnNewRecord()
        {
            if (!ValidateNewPermission()) return;
            
            // Override in derived forms
            PerformNewRecord();
        }
        
        protected virtual void OnSaveRecord()
        {
            if (!ValidateEditPermission()) return;
            
            // Override in derived forms
            PerformSaveRecord();
        }
        
        protected virtual void OnDeleteRecord()
        {
            if (!ValidateDeletePermission()) return;
            
            // Confirm deletion
            var result = MessageBox.Show("Are you sure you want to delete this record?", 
                "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                // Override in derived forms
                PerformDeleteRecord();
            }
        }
        
        protected virtual void OnPrintRecord()
        {
            if (!ValidatePrintPermission()) return;
            
            // Override in derived forms
            PerformPrintRecord();
        }
        
        // Virtual methods for derived forms to override
        protected virtual void PerformNewRecord() { }
        protected virtual void PerformSaveRecord() { }
        protected virtual void PerformDeleteRecord() { }
        protected virtual void PerformPrintRecord() { }
        
        #endregion
    }
}
```

### FormPermissionHelper.cs
```csharp
using System;
using System.Collections.Generic;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Modules.Helpers
{
    public static class FormPermissionHelper
    {
        /// <summary>
        /// Apply permissions to a form's controls
        /// </summary>
        public static void ApplyFormPermissions(Form form, int userId, string formName)
        {
            try
            {
                var hasEdit = PermissionService.HasPermission(userId, formName, PermissionType.Edit);
                var hasNew = PermissionService.HasPermission(userId, formName, PermissionType.New);
                var hasDelete = PermissionService.HasPermission(userId, formName, PermissionType.Delete);
                var hasPrint = PermissionService.HasPermission(userId, formName, PermissionType.Print);
                
                ApplyControlPermissions(form, hasEdit, hasNew, hasDelete, hasPrint);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying form permissions: {ex.Message}");
                // Default to read-only for security
                ApplyControlPermissions(form, false, false, false, false);
            }
        }
        
        /// <summary>
        /// Apply permissions to specific controls
        /// </summary>
        private static void ApplyControlPermissions(Control parent, bool canEdit, bool canNew, bool canDelete, bool canPrint)
        {
            foreach (Control control in parent.Controls)
            {
                switch (control.Name.ToLower())
                {
                    case var name when name.Contains("new") || name.Contains("add"):
                        control.Enabled = canNew;
                        break;
                    case var name when name.Contains("save") || name.Contains("update"):
                        control.Enabled = canEdit;
                        break;
                    case var name when name.Contains("delete") || name.Contains("remove"):
                        control.Enabled = canDelete;
                        break;
                    case var name when name.Contains("print") || name.Contains("report"):
                        control.Enabled = canPrint;
                        break;
                }
                
                // Recursively process child controls
                if (control.HasChildren)
                {
                    ApplyControlPermissions(control, canEdit, canNew, canDelete, canPrint);
                }
            }
        }
        
        /// <summary>
        /// Set grid permissions
        /// </summary>
        public static void ApplyGridPermissions(GridControl gridControl, int userId, string formName)
        {
            try
            {
                var hasEdit = PermissionService.HasPermission(userId, formName, PermissionType.Edit);
                var hasNew = PermissionService.HasPermission(userId, formName, PermissionType.New);
                var hasDelete = PermissionService.HasPermission(userId, formName, PermissionType.Delete);
                
                if (gridControl.MainView is DevExpress.XtraGrid.Views.Grid.GridView gridView)
                {
                    gridView.OptionsBehavior.Editable = hasEdit;
                    gridView.OptionsView.NewItemRowPosition = hasNew 
                        ? DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom 
                        : DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.None;
                    
                    // Configure delete key behavior
                    gridView.OptionsSelection.EnableAppearanceFocusedRow = hasDelete;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying grid permissions: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Show permission status in form
        /// </summary>
        public static void ShowPermissionStatus(Form form, int userId, string formName)
        {
            try
            {
                var permissions = new[]
                {
                    new { Type = "Read", Has = PermissionService.HasPermission(userId, formName, PermissionType.Read) },
                    new { Type = "New", Has = PermissionService.HasPermission(userId, formName, PermissionType.New) },
                    new { Type = "Edit", Has = PermissionService.HasPermission(userId, formName, PermissionType.Edit) },
                    new { Type = "Delete", Has = PermissionService.HasPermission(userId, formName, PermissionType.Delete) },
                    new { Type = "Print", Has = PermissionService.HasPermission(userId, formName, PermissionType.Print) }
                };
                
                var statusText = "Permissions: ";
                var permList = new List<string>();
                
                foreach (var perm in permissions)
                {
                    if (perm.Has)
                    {
                        permList.Add(perm.Type);
                    }
                }
                
                statusText += permList.Count > 0 ? string.Join(", ", permList) : "None";
                
                // Update form title or status bar
                if (form.Text.Contains("["))
                {
                    form.Text = form.Text.Split('[')[0].Trim() + $" [{statusText}]";
                }
                else
                {
                    form.Text += $" [{statusText}]";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing permission status: {ex.Message}");
            }
        }
    }
}
```

### Example: Enhanced UserMasterForm
```csharp
// Update existing UserMasterForm to inherit from BaseFormWithPermissions
public partial class UserMasterForm : BaseFormWithPermissions
{
    public UserMasterForm() : base("UserMasterForm")
    {
        InitializeComponent();
        
        // Set button references for automatic permission handling
        btnNew = this.btnNewUser;
        btnSave = this.btnSaveUser;
        btnDelete = this.btnDeleteUser;
        btnPrint = this.btnPrintUser;
    }
    
    protected override void PerformNewRecord()
    {
        // Existing new user logic
        ClearForm();
        SetFormMode(FormMode.New);
    }
    
    protected override void PerformSaveRecord()
    {
        // Existing save user logic
        if (ValidateUserData())
        {
            SaveUser();
        }
    }
    
    protected override void PerformDeleteRecord()
    {
        // Existing delete user logic
        if (GetCurrentUserId() > 0)
        {
            DeleteUser(GetCurrentUserId());
        }
    }
    
    protected override void PerformPrintRecord()
    {
        // Existing print user logic
        PrintUserReport();
    }
    
    protected override void SetFormReadOnly()
    {
        base.SetFormReadOnly();
        
        // Additional UserMasterForm-specific read-only logic
        tabControlUser.Enabled = false; // Disable tab switching
        // Keep navigation buttons enabled
        btnFirst.Enabled = true;
        btnPrevious.Enabled = true;
        btnNext.Enabled = true;
        btnLast.Enabled = true;
    }
    
    // Override button click events to use base class methods
    private void btnNewUser_Click(object sender, EventArgs e)
    {
        OnNewRecord();
    }
    
    private void btnSaveUser_Click(object sender, EventArgs e)
    {
        OnSaveRecord();
    }
    
    private void btnDeleteUser_Click(object sender, EventArgs e)
    {
        OnDeleteRecord();
    }
    
    private void btnPrintUser_Click(object sender, EventArgs e)
    {
        OnPrintRecord();
    }
}
```

## Integration Steps

### 1. Update Existing Forms
Modify existing MainForms to inherit from BaseFormWithPermissions:
- UserMasterForm
- RoleMasterForm
- DatabaseForm
- ParametersForm
- SQLQueryForm
- UserManagementListForm

### 2. Add Permission Checks to Custom Operations
For forms with custom operations, add permission validation:
```csharp
private void CustomOperation()
{
    if (!HasPermission(PermissionType.Edit))
    {
        ShowInsufficientPermissionMessage("perform this operation");
        return;
    }
    
    // Perform operation
}
```

### 3. Update Grid Operations
For forms with grids, apply grid permissions:
```csharp
protected override void OnLoad(EventArgs e)
{
    base.OnLoad(e);
    
    FormPermissionHelper.ApplyGridPermissions(gridControlMain, CurrentUserId, FormName);
}
```

## Acceptance Criteria

- [ ] Base form class with automatic permission checking
- [ ] Permission validation for New/Edit/Delete/Print operations
- [ ] Read-only mode when edit permission is denied
- [ ] Form title updates with permission indicators
- [ ] Grid permission enforcement
- [ ] Graceful error handling for permission failures
- [ ] Integration with existing forms
- [ ] Consistent permission checking across all forms
- [ ] User-friendly permission denial messages

## Dependencies
- Task 06: Core Permission Service Logic
- Task 13: MainFrame Ribbon Permission Filtering

## Next Tasks
This task enables:
- Task 15: Global Permission Implementation
- Task 16: Testing and Validation Suite
